'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Navigation, AlertCircle, Ticket, Thermometer } from 'lucide-react'
import { usePathname } from 'next/navigation'

export default function LondonNYEGuide() {
  const t = useTranslations();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('londonNYE.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('londonNYE.title')}</h1>
        <p className="text-slate-500 mb-6 text-base md:text-lg">{t('londonNYE.subtitle')}</p>
        
        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=800&h=400&fit=crop"
            alt={t('londonNYE.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('londonNYE.date')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('londonNYE.location')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            {/* 简介 */}
            <div className="mb-8">
              <p className="text-lg text-slate-300 mb-4">{t('londonNYE.intro')}</p>
            </div>

            {/* 观赏地点 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {t('londonNYE.viewingSpots.title')}
              </h2>
              
              {/* 官方付费区域 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-blue-400">{t('londonNYE.viewingSpots.official.title')}</h3>
                <p className="text-slate-300 mb-4">{t('londonNYE.viewingSpots.official.description')}</p>
                <div className="bg-blue-900/30 rounded p-3 border border-blue-400/30">
                  <div className="text-blue-400 font-bold text-sm">{t('londonNYE.viewingSpots.official.pricing')}</div>
                  <div className="text-slate-300 text-xs">{t('londonNYE.viewingSpots.official.details')}</div>
                </div>
              </div>

              {/* 泰晤士河游船 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-purple-400">{t('londonNYE.viewingSpots.cruises.title')}</h3>
                <p className="text-slate-300 mb-4">{t('londonNYE.viewingSpots.cruises.description')}</p>
              </div>

              {/* 屋顶和天际线位置 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-orange-400">{t('londonNYE.viewingSpots.rooftop.title')}</h3>
                <p className="text-slate-300 mb-4">{t('londonNYE.viewingSpots.rooftop.description')}</p>
                <ul className="text-slate-300 space-y-2">
                  <li>• {t('londonNYE.viewingSpots.rooftop.location1')}</li>
                  <li>• {t('londonNYE.viewingSpots.rooftop.location2')}</li>
                  <li>• {t('londonNYE.viewingSpots.rooftop.location3')}</li>
                </ul>
              </div>

              {/* 免费但拥挤的地点 */}
              <div className="bg-slate-800/50 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-green-400">{t('londonNYE.viewingSpots.free.title')}</h3>
                <p className="text-slate-300 mb-4">{t('londonNYE.viewingSpots.free.description')}</p>
                <ul className="text-slate-300 space-y-2">
                  <li>• {t('londonNYE.viewingSpots.free.location1')}</li>
                  <li>• {t('londonNYE.viewingSpots.free.location2')}</li>
                  <li>• {t('londonNYE.viewingSpots.free.location3')}</li>
                </ul>
                <div className="mt-4 p-3 bg-green-900/30 rounded border-l-4 border-green-400">
                  <p className="text-green-200 text-sm">{t('londonNYE.viewingSpots.free.tip')}</p>
                </div>
              </div>
            </section>

            {/* 交通出行 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Navigation className="w-6 h-6 mr-2 text-blue-400" />
                {t('londonNYE.transport.title')}
              </h2>
              
              <div className="bg-slate-800/50 rounded-lg p-6 mb-4">
                <h3 className="text-xl font-bold mb-3 text-blue-400">{t('londonNYE.transport.public.title')}</h3>
                <p className="text-slate-300 mb-4">{t('londonNYE.transport.public.description')}</p>
              </div>

              <div className="bg-slate-800/50 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-yellow-400">{t('londonNYE.transport.weather.title')}</h3>
                <p className="text-slate-300">{t('londonNYE.transport.weather.description')}</p>
              </div>
            </section>

            {/* 实用贴士 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('londonNYE.tips.title')}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('londonNYE.tips.booking.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('londonNYE.tips.booking.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('londonNYE.tips.timing.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('londonNYE.tips.timing.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('londonNYE.tips.weather.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('londonNYE.tips.weather.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('londonNYE.tips.crowds.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('londonNYE.tips.crowds.description')}</p>
                </div>
              </div>
            </section>

            {/* 体验总结 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('londonNYE.summary.title')}</h2>
              <div className="bg-gradient-to-r from-purple-900/50 to-blue-900/50 rounded-lg p-6">
                <p className="text-slate-200 text-lg leading-relaxed">{t('londonNYE.summary.description')}</p>
              </div>
            </section>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 快速信息 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('londonNYE.quickInfo.title')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-slate-300">{t('londonNYE.quickInfo.date')}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-green-400" />
                  <span className="text-slate-300">{t('londonNYE.quickInfo.time')}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-red-400" />
                  <span className="text-slate-300">{t('londonNYE.quickInfo.location')}</span>
                </div>
                <div className="flex items-center">
                  <Thermometer className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-slate-300">{t('londonNYE.quickInfo.weather')}</span>
                </div>
                <div className="flex items-center">
                  <Ticket className="w-4 h-4 mr-2 text-purple-400" />
                  <span className="text-slate-300">{t('londonNYE.quickInfo.cost')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('londonNYE.relatedLinks.title')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/sydney-nye-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('londonNYE.relatedLinks.sydney')}</span>
                </Link>
                <Link href={`/${locale}/dubai-nye-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('londonNYE.relatedLinks.dubai')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
