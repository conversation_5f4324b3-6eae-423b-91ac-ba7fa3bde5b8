"use client"

import type * as React from "react"
import { format } from "date-fns"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Sparkles, MapPin, Calendar } from "lucide-react"

interface DateSpecificCardsProps {
  date: Date
  textClassName?: string
}

interface SpecificCard {
  date: Date
  content: string
  location?: string // Optional location field
  title?: string // Optional title field
}

// Improved card data structure for easier future additions
const specificCardsData: SpecificCard[] = [
  {
    date: new Date(2025, 6, 15), // Month is 0-indexed (July is 6)
    title: "测试烟花表演",
    content: "Card Content 1 for 2025-07-15",
    location: "测试地点",
  },
  {
    date: new Date(2025, 7, 4), // August is 7
    title: "夏日烟花节",
    content: "Card Content 2 for 2025-08-04",
    location: "测试城市",
  },
  // Add more cards here following the same structure:
  // {
  //   date: new Date(YYYY, MM - 1, DD), // Remember month is 0-indexed
  //   title: "Event Title",
  //   content: "Your custom content here",
  //   location: "Event Location",
  // },
  {
    date: new Date(2025, 6, 18),
    title: "镰仓烟花大会",
    content: "Kamakura Fireworks Festival - 传统日式烟花表演，结合海滨美景",
    location: "日本 镰仓",
  },
  {
    date: new Date(2025, 6, 19),
    title: "温哥华光之庆典",
    content: "Honda Celebration of Light – 场次1，国际烟花竞赛",
    location: "加拿大 温哥华",
  },
  {
    date: new Date(2025, 6, 21),
    title: "伊势神宫烟花节",
    content: "伊势神宫烟花 & 名古屋港烟火节，双城联动庆典",
    location: "日本 三重 / 名古屋",
  },
  {
    date: new Date(2025, 6, 23),
    title: "温哥华光之庆典",
    content: "Celebration of Light – 场次2，精彩继续",
    location: "加拿大 温哥华",
  },
  // 7月24–25日
  {
    date: new Date(2025, 6, 24),
    title: "日本夏日烟火祭",
    content: "十日市、天神祭、札幌花火等多场日本烟火大会同时举行",
    location: "日本 大阪、北海道等",
  },
  {
    date: new Date(2025, 6, 25),
    title: "日本夏日烟火祭",
    content: "十日市、天神祭、札幌花火等多场日本烟火大会同时举行",
    location: "日本 大阪、北海道等",
  },
  // 7月25–26日
  {
    date: new Date(2025, 6, 25),
    title: "关东烟花巡礼",
    content: "镰仓、热海、东京、长野等地多场烟花大会，夏日烟火盛宴",
    location: "日本 关东多地",
  },
  {
    date: new Date(2025, 6, 26),
    title: "关东烟花巡礼",
    content: "镰仓、热海、东京、长野等地多场烟花大会，夏日烟火盛宴",
    location: "日本 关东多地",
  },
  // 7月26日
  {
    date: new Date(2025, 6, 26),
    title: "双城烟花盛典",
    content: "温哥华 Celebration of Light 场次3 + 隅田川烟花大会",
    location: "加拿大温哥华 & 日本东京",
  },
  // 8月2–3日
  {
    date: new Date(2025, 7, 2),
    title: "长冈烟花大会",
    content: "Nagaoka Fireworks Festival - 日本三大烟花大会之一",
    location: "日本 新潟",
  },
  {
    date: new Date(2025, 7, 3),
    title: "长冈烟花大会",
    content: "Nagaoka Fireworks Festival - 日本三大烟花大会之一",
    location: "日本 新潟",
  },
  // 8月6–8日
  {
    date: new Date(2025, 7, 6),
    title: "关西湖畔烟火祭",
    content: "湖北＆滋贺多个湖边及市区大型烟火，水面倒影美景",
    location: "日本 滋贺等",
  },
  {
    date: new Date(2025, 7, 7),
    title: "关西湖畔烟火祭",
    content: "湖北＆滋贺多个湖边及市区大型烟火，水面倒影美景",
    location: "日本 滋贺等",
  },
  {
    date: new Date(2025, 7, 8),
    title: "关西湖畔烟火祭",
    content: "湖北＆滋贺多个湖边及市区大型烟火，水面倒影美景",
    location: "日本 滋贺等",
  },
  // 8月30日
  {
    date: new Date(2025, 7, 30),
    title: "大曲烟火比赛",
    content: "Omagari Fireworks Festival - 全国烟火竞技大会",
    location: "日本 秋田",
  },
  // 9月13日
  {
    date: new Date(2025, 8, 13),
    title: "Katakai 大尺玉烟火节",
    content: "世界最大级别的四尺玉烟花表演",
    location: "日本 新潟 Katakai",
  },
]

const DateSpecificCards: React.FC<DateSpecificCardsProps> = ({ date, textClassName = "" }) => {
  const formattedCurrentDate = format(date, "yyyy-MM-dd")

  const cardsToDisplay = specificCardsData.filter((card) => format(card.date, "yyyy-MM-dd") === formattedCurrentDate)

  return (
    <div className="p-4 min-w-[300px]">
      {cardsToDisplay.length > 0 ? (
        cardsToDisplay.map((card, index) => (
          <Card key={index} className="mb-4 bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {/* 烟花图标 */}
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="flex-1 min-w-0">
                  {/* 标题 */}
                  <h3 className={`font-bold text-lg mb-2 ${textClassName}`}>
                    {card.title || `Information for ${formattedCurrentDate}`}
                  </h3>

                  {/* 日期信息 */}
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-4 h-4 text-slate-400" />
                    <span className="text-sm text-slate-400">{formattedCurrentDate}</span>
                  </div>

                  {/* 地点信息 */}
                  {card.location && (
                    <div className="flex items-center gap-2 mb-3">
                      <MapPin className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-400">{card.location}</span>
                    </div>
                  )}

                  {/* 描述内容 */}
                  <p className={`text-sm leading-relaxed ${textClassName}`}>
                    {card.content}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gradient-to-br from-slate-700 to-slate-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-slate-400" />
          </div>
          <p className={"text-slate-400 " + textClassName}>No specific cards for {formattedCurrentDate}.</p>
        </div>
      )}
    </div>
  )
}

export default DateSpecificCards 