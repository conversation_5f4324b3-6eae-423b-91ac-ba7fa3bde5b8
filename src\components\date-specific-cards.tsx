"use client"

import type * as React from "react"
import { format } from "date-fns"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON>rk<PERSON>, MapPin, Calendar } from "lucide-react"
import { useTranslations } from 'next-intl'

interface DateSpecificCardsProps {
  date: Date
  textClassName?: string
}

interface SpecificCard {
  date: Date
  content: string
  location?: string // Optional location field
  title?: string // Optional title field
}

// Date keys for translation lookup
const cardDateKeys = [
  { date: new Date(2025, 6, 15), key: "2025-07-15" },
  { date: new Date(2025, 7, 4), key: "2025-08-04" },
  { date: new Date(2025, 6, 18), key: "2025-07-18" },
  { date: new Date(2025, 6, 19), key: "2025-07-19" },
  { date: new Date(2025, 6, 21), key: "2025-07-21" },
  { date: new Date(2025, 6, 23), key: "2025-07-23" },
  { date: new Date(2025, 6, 24), key: "2025-07-24" },
  { date: new Date(2025, 6, 25), key: "2025-07-25" },
  { date: new Date(2025, 6, 25), key: "2025-07-25-2" }, // Multiple events on same day
  { date: new Date(2025, 6, 26), key: "2025-07-26" },
  { date: new Date(2025, 6, 26), key: "2025-07-26-2" }, // Multiple events on same day
  { date: new Date(2025, 7, 2), key: "2025-08-02" },
  { date: new Date(2025, 7, 3), key: "2025-08-03" },
  { date: new Date(2025, 7, 6), key: "2025-08-06" },
  { date: new Date(2025, 7, 7), key: "2025-08-07" },
  { date: new Date(2025, 7, 8), key: "2025-08-08" },
  { date: new Date(2025, 7, 30), key: "2025-08-30" },
  { date: new Date(2025, 8, 13), key: "2025-09-13" },
]

const DateSpecificCards: React.FC<DateSpecificCardsProps> = ({ date, textClassName = "" }) => {
  const t = useTranslations('calendar')
  const formattedCurrentDate = format(date, "yyyy-MM-dd")

  // Find matching cards for the current date
  const matchingCardKeys = cardDateKeys.filter((item) => format(item.date, "yyyy-MM-dd") === formattedCurrentDate)

  // Build cards from translation data
  const cardsToDisplay: SpecificCard[] = matchingCardKeys.map((item) => {
    const cardData = t.raw(`cards.${item.key}`)
    return {
      date: item.date,
      title: cardData?.title || `Information for ${formattedCurrentDate}`,
      content: cardData?.content || `No content available for ${formattedCurrentDate}`,
      location: cardData?.location || undefined
    }
  }).filter(card => card.title && card.content) // Filter out cards without translation data

  return (
    <div className="p-4 min-w-[300px]">
      {cardsToDisplay.length > 0 ? (
        cardsToDisplay.map((card, index) => (
          <Card key={index} className="mb-4 bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {/* 烟花图标 */}
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                    <Sparkles className="w-6 h-6 text-white" />
                  </div>
                </div>

                {/* 内容区域 */}
                <div className="flex-1 min-w-0">
                  {/* 标题 */}
                  <h3 className={`font-bold text-lg mb-2 ${textClassName}`}>
                    {card.title}
                  </h3>

                  {/* 日期信息 */}
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-4 h-4 text-slate-400" />
                    <span className="text-sm text-slate-400">{formattedCurrentDate}</span>
                  </div>

                  {/* 地点信息 */}
                  {card.location && (
                    <div className="flex items-center gap-2 mb-3">
                      <MapPin className="w-4 h-4 text-slate-400" />
                      <span className="text-sm text-slate-400">{card.location}</span>
                    </div>
                  )}

                  {/* 描述内容 */}
                  <p className={`text-sm leading-relaxed ${textClassName}`}>
                    {card.content}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      ) : (
        <div className="text-center py-8">
          <div className="w-16 h-16 bg-gradient-to-br from-slate-700 to-slate-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-slate-400" />
          </div>
          <p className={"text-slate-400 " + textClassName}>
            {t('noEvents', { date: formattedCurrentDate }) || `No specific cards for ${formattedCurrentDate}.`}
          </p>
        </div>
      )}
    </div>
  )
}

export default DateSpecificCards 