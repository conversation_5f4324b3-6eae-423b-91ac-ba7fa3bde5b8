'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Navigation, AlertCircle, Ticket, Train, Camera } from 'lucide-react'
import { usePathname } from 'next/navigation'

export default function SydneyTravelGuide() {
  const t = useTranslations();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('sydneyTravelGuide.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('sydneyTravelGuide.title')}</h1>
        <p className="text-black mb-6 text-base md:text-lg">{t('sydneyTravelGuide.subtitle')}</p>
        
        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop"
            alt={t('sydneyTravelGuide.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('sydneyTravelGuide.date')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('sydneyTravelGuide.location')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            {/* 简介 */}
            <div className="mb-8">
              <p className="text-lg text-black mb-4">{t('sydneyTravelGuide.intro')}</p>
            </div>

            {/* 最佳观赏点推荐 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {t('sydneyTravelGuide.viewingSpots.title')}
              </h2>
              
              {/* 免费观赏点 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-green-400">{t('sydneyTravelGuide.viewingSpots.free.title')}</h3>
                <p className="text-slate-300 mb-4">{t('sydneyTravelGuide.viewingSpots.free.description')}</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-green-900/30 rounded p-3 border border-green-400/30">
                    <h4 className="font-bold text-green-400 text-sm mb-2">{t('sydneyTravelGuide.viewingSpots.free.spot1.name')}</h4>
                    <p className="text-slate-300 text-xs">{t('sydneyTravelGuide.viewingSpots.free.spot1.description')}</p>
                  </div>
                  <div className="bg-green-900/30 rounded p-3 border border-green-400/30">
                    <h4 className="font-bold text-green-400 text-sm mb-2">{t('sydneyTravelGuide.viewingSpots.free.spot2.name')}</h4>
                    <p className="text-slate-300 text-xs">{t('sydneyTravelGuide.viewingSpots.free.spot2.description')}</p>
                  </div>
                  <div className="bg-green-900/30 rounded p-3 border border-green-400/30">
                    <h4 className="font-bold text-green-400 text-sm mb-2">{t('sydneyTravelGuide.viewingSpots.free.spot3.name')}</h4>
                    <p className="text-slate-300 text-xs">{t('sydneyTravelGuide.viewingSpots.free.spot3.description')}</p>
                  </div>
                  <div className="bg-green-900/30 rounded p-3 border border-green-400/30">
                    <h4 className="font-bold text-green-400 text-sm mb-2">{t('sydneyTravelGuide.viewingSpots.free.spot4.name')}</h4>
                    <p className="text-slate-300 text-xs">{t('sydneyTravelGuide.viewingSpots.free.spot4.description')}</p>
                  </div>
                </div>
              </div>

              {/* 付费观赏点 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-blue-400">{t('sydneyTravelGuide.viewingSpots.paid.title')}</h3>
                <div className="space-y-4">
                  <div className="bg-blue-900/30 rounded p-4 border border-blue-400/30">
                    <h4 className="font-bold text-blue-400 mb-2">{t('sydneyTravelGuide.viewingSpots.paid.gardens.name')}</h4>
                    <p className="text-slate-300 text-sm">{t('sydneyTravelGuide.viewingSpots.paid.gardens.description')}</p>
                  </div>
                  <div className="bg-blue-900/30 rounded p-4 border border-blue-400/30">
                    <h4 className="font-bold text-blue-400 mb-2">{t('sydneyTravelGuide.viewingSpots.paid.cruise.name')}</h4>
                    <p className="text-slate-300 text-sm">{t('sydneyTravelGuide.viewingSpots.paid.cruise.description')}</p>
                    <div className="mt-2 text-yellow-400 font-bold text-sm">{t('sydneyTravelGuide.viewingSpots.paid.cruise.price')}</div>
                  </div>
                  <div className="bg-blue-900/30 rounded p-4 border border-blue-400/30">
                    <h4 className="font-bold text-blue-400 mb-2">{t('sydneyTravelGuide.viewingSpots.paid.parties.name')}</h4>
                    <p className="text-slate-300 text-sm">{t('sydneyTravelGuide.viewingSpots.paid.parties.description')}</p>
                  </div>
                </div>
              </div>
            </section>

            {/* 交通与出行建议 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Train className="w-6 h-6 mr-2 text-blue-400" />
                {t('sydneyTravelGuide.transport.title')}
              </h2>
              
              <div className="space-y-4">
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">{t('sydneyTravelGuide.transport.public.title')}</h3>
                  <p className="text-slate-300 mb-4">{t('sydneyTravelGuide.transport.public.description')}</p>
                  <div className="bg-blue-900/30 rounded p-3 border-l-4 border-blue-400">
                    <p className="text-blue-200 text-sm">{t('sydneyTravelGuide.transport.public.note')}</p>
                  </div>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-orange-400">{t('sydneyTravelGuide.transport.timing.title')}</h3>
                  <p className="text-slate-300 mb-4">{t('sydneyTravelGuide.transport.timing.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-red-400">{t('sydneyTravelGuide.transport.roads.title')}</h3>
                  <p className="text-slate-300">{t('sydneyTravelGuide.transport.roads.description')}</p>
                </div>
              </div>
            </section>

            {/* 装备准备 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('sydneyTravelGuide.equipment.title')}
              </h2>
              
              <div className="bg-slate-800/50 rounded-lg p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-bold text-yellow-400 mb-2">{t('sydneyTravelGuide.equipment.essentials.title')}</h4>
                    <ul className="text-slate-300 text-sm space-y-1">
                      <li>• {t('sydneyTravelGuide.equipment.essentials.item1')}</li>
                      <li>• {t('sydneyTravelGuide.equipment.essentials.item2')}</li>
                      <li>• {t('sydneyTravelGuide.equipment.essentials.item3')}</li>
                      <li>• {t('sydneyTravelGuide.equipment.essentials.item4')}</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-bold text-yellow-400 mb-2">{t('sydneyTravelGuide.equipment.rules.title')}</h4>
                    <p className="text-slate-300 text-sm">{t('sydneyTravelGuide.equipment.rules.description')}</p>
                  </div>
                </div>
              </div>
            </section>

            {/* 活动流程与氛围 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Clock className="w-6 h-6 mr-2 text-purple-400" />
                {t('sydneyTravelGuide.schedule.title')}
              </h2>
              
              <div className="space-y-4">
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-purple-400">{t('sydneyTravelGuide.schedule.calling.title')}</h3>
                  <p className="text-slate-300">{t('sydneyTravelGuide.schedule.calling.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-purple-400">{t('sydneyTravelGuide.schedule.main.title')}</h3>
                  <p className="text-slate-300">{t('sydneyTravelGuide.schedule.main.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-purple-400">{t('sydneyTravelGuide.schedule.other.title')}</h3>
                  <p className="text-slate-300">{t('sydneyTravelGuide.schedule.other.description')}</p>
                </div>
              </div>
            </section>

            {/* 家庭亲子专属贴士 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Users className="w-6 h-6 mr-2 text-green-400" />
                {t('sydneyTravelGuide.family.title')}
              </h2>
              
              <div className="bg-gradient-to-r from-green-900/50 to-blue-900/50 rounded-lg p-6">
                <ul className="text-slate-200 space-y-3">
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    {t('sydneyTravelGuide.family.tip1')}
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    {t('sydneyTravelGuide.family.tip2')}
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    {t('sydneyTravelGuide.family.tip3')}
                  </li>
                </ul>
              </div>
            </section>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 快速信息 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('sydneyTravelGuide.quickInfo.title')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-slate-300">{t('sydneyTravelGuide.quickInfo.date')}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-green-400" />
                  <span className="text-slate-300">{t('sydneyTravelGuide.quickInfo.time')}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-red-400" />
                  <span className="text-slate-300">{t('sydneyTravelGuide.quickInfo.location')}</span>
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-2 text-purple-400" />
                  <span className="text-slate-300">{t('sydneyTravelGuide.quickInfo.crowd')}</span>
                </div>
                <div className="flex items-center">
                  <Ticket className="w-4 h-4 mr-2 text-yellow-400" />
                  <span className="text-slate-300">{t('sydneyTravelGuide.quickInfo.cost')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('sydneyTravelGuide.relatedLinks.title')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/dubai-travel-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('sydneyTravelGuide.relatedLinks.dubai')}</span>
                </Link>
                <Link href={`/${locale}/europe-travel-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('sydneyTravelGuide.relatedLinks.europe')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
