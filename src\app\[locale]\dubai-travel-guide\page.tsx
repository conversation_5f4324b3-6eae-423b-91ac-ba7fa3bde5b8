'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Navigation, AlertCircle, Ticket, Hotel, Ship } from 'lucide-react'
import { usePathname } from 'next/navigation'

export default function DubaiTravelGuide() {
  const t = useTranslations();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('dubaiTravelGuide.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('dubaiTravelGuide.title')}</h1>
        <p className="text-slate-500 mb-6 text-base md:text-lg">{t('dubaiTravelGuide.subtitle')}</p>
        
        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=800&h=400&fit=crop"
            alt={t('dubaiTravelGuide.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('dubaiTravelGuide.date')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('dubaiTravelGuide.location')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            {/* 简介 */}
            <div className="mb-8">
              <p className="text-lg text-slate-300 mb-4">{t('dubaiTravelGuide.intro')}</p>
            </div>

            {/* 住宿选择 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Hotel className="w-6 h-6 mr-2 text-yellow-400" />
                {t('dubaiTravelGuide.accommodation.title')}
              </h2>
              
              <div className="space-y-4">
                {/* Downtown Dubai 豪华首选 */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-yellow-400">{t('dubaiTravelGuide.accommodation.downtown.title')}</h3>
                  <div className="space-y-3">
                    <div className="bg-yellow-900/30 rounded p-4 border border-yellow-400/30">
                      <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiTravelGuide.accommodation.downtown.luxury.name')}</h4>
                      <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.accommodation.downtown.luxury.description')}</p>
                    </div>
                    <div className="bg-yellow-900/30 rounded p-4 border border-yellow-400/30">
                      <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiTravelGuide.accommodation.downtown.mall.name')}</h4>
                      <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.accommodation.downtown.mall.description')}</p>
                    </div>
                  </div>
                </div>

                {/* Palm Jumeirah 风格体验 */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">{t('dubaiTravelGuide.accommodation.palm.title')}</h3>
                  <div className="bg-blue-900/30 rounded p-4 border border-blue-400/30">
                    <h4 className="font-bold text-blue-400 mb-2">{t('dubaiTravelGuide.accommodation.palm.atlantis.name')}</h4>
                    <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.accommodation.palm.atlantis.description')}</p>
                  </div>
                </div>

                {/* JBR 海滩度假方式 */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-cyan-400">{t('dubaiTravelGuide.accommodation.jbr.title')}</h3>
                  <div className="bg-cyan-900/30 rounded p-4 border border-cyan-400/30">
                    <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.accommodation.jbr.description')}</p>
                  </div>
                </div>
              </div>
            </section>

            {/* 观赏点规划 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {t('dubaiTravelGuide.viewingSpots.title')}
              </h2>
              
              <div className="space-y-4">
                {/* Burj Park 票务 */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-yellow-400">{t('dubaiTravelGuide.viewingSpots.burjPark.title')}</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-yellow-900/30 rounded p-3 border border-yellow-400/30">
                      <div className="text-yellow-400 font-bold text-sm">{t('dubaiTravelGuide.viewingSpots.burjPark.adult.price')}</div>
                      <div className="text-slate-300 text-xs">{t('dubaiTravelGuide.viewingSpots.burjPark.adult.details')}</div>
                    </div>
                    <div className="bg-blue-900/30 rounded p-3 border border-blue-400/30">
                      <div className="text-blue-400 font-bold text-sm">{t('dubaiTravelGuide.viewingSpots.burjPark.child.price')}</div>
                      <div className="text-slate-300 text-xs">{t('dubaiTravelGuide.viewingSpots.burjPark.child.details')}</div>
                    </div>
                    <div className="bg-green-900/30 rounded p-3 border border-green-400/30">
                      <div className="text-green-400 font-bold text-sm">{t('dubaiTravelGuide.viewingSpots.burjPark.under5.price')}</div>
                      <div className="text-slate-300 text-xs">{t('dubaiTravelGuide.viewingSpots.burjPark.under5.details')}</div>
                    </div>
                  </div>
                  <p className="text-slate-300">{t('dubaiTravelGuide.viewingSpots.burjPark.description')}</p>
                </div>

                {/* Downtown 免费区 */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-green-400">{t('dubaiTravelGuide.viewingSpots.free.title')}</h3>
                  <p className="text-slate-300 mb-4">{t('dubaiTravelGuide.viewingSpots.free.description')}</p>
                  <div className="bg-green-900/30 rounded p-3 border-l-4 border-green-400">
                    <p className="text-green-200 text-sm">{t('dubaiTravelGuide.viewingSpots.free.tip')}</p>
                  </div>
                </div>

                {/* 豪华巡游体验 */}
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-purple-400">{t('dubaiTravelGuide.viewingSpots.cruise.title')}</h3>
                  <p className="text-slate-300">{t('dubaiTravelGuide.viewingSpots.cruise.description')}</p>
                </div>
              </div>
            </section>

            {/* 交通技巧与攻略 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Navigation className="w-6 h-6 mr-2 text-blue-400" />
                {t('dubaiTravelGuide.transport.title')}
              </h2>
              
              <div className="space-y-4">
                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-blue-400">{t('dubaiTravelGuide.transport.metro.title')}</h3>
                  <p className="text-slate-300 mb-4">{t('dubaiTravelGuide.transport.metro.description')}</p>
                  <div className="bg-blue-900/30 rounded p-3 border-l-4 border-blue-400">
                    <p className="text-blue-200 text-sm">{t('dubaiTravelGuide.transport.metro.stations')}</p>
                  </div>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-orange-400">{t('dubaiTravelGuide.transport.roads.title')}</h3>
                  <p className="text-slate-300">{t('dubaiTravelGuide.transport.roads.description')}</p>
                </div>

                <div className="bg-slate-800/50 rounded-lg p-6">
                  <h3 className="text-xl font-bold mb-3 text-yellow-400">{t('dubaiTravelGuide.transport.taxi.title')}</h3>
                  <p className="text-slate-300">{t('dubaiTravelGuide.transport.taxi.description')}</p>
                </div>
              </div>
            </section>

            {/* 活动节奏与贴士 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('dubaiTravelGuide.tips.title')}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiTravelGuide.tips.tickets.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.tips.tickets.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiTravelGuide.tips.schedule.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.tips.schedule.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiTravelGuide.tips.weather.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.tips.weather.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiTravelGuide.tips.safety.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiTravelGuide.tips.safety.description')}</p>
                </div>
              </div>
            </section>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 快速信息 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('dubaiTravelGuide.quickInfo.title')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-slate-300">{t('dubaiTravelGuide.quickInfo.date')}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-green-400" />
                  <span className="text-slate-300">{t('dubaiTravelGuide.quickInfo.time')}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-red-400" />
                  <span className="text-slate-300">{t('dubaiTravelGuide.quickInfo.location')}</span>
                </div>
                <div className="flex items-center">
                  <Ticket className="w-4 h-4 mr-2 text-yellow-400" />
                  <span className="text-slate-300">{t('dubaiTravelGuide.quickInfo.cost')}</span>
                </div>
                <div className="flex items-center">
                  <Hotel className="w-4 h-4 mr-2 text-purple-400" />
                  <span className="text-slate-300">{t('dubaiTravelGuide.quickInfo.accommodation')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('dubaiTravelGuide.relatedLinks.title')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/sydney-travel-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('dubaiTravelGuide.relatedLinks.sydney')}</span>
                </Link>
                <Link href={`/${locale}/europe-travel-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('dubaiTravelGuide.relatedLinks.europe')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
