import { useTranslations, useLocale } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Navigation, AlertCircle, Ticket, DollarSign } from 'lucide-react'

export default function DubaiNYEGuide() {
  const t = useTranslations();
  const locale = useLocale();
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('dubaiNYE.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('dubaiNYE.title')}</h1>
        <p className="text-slate-500 mb-6 text-base md:text-lg">{t('dubaiNYE.subtitle')}</p>
        
        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=800&h=400&fit=crop"
            alt={t('dubaiNYE.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('dubaiNYE.date')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('dubaiNYE.location')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            {/* 简介 */}
            <div className="mb-8">
              <p className="text-lg text-slate-300 mb-4">{t('dubaiNYE.intro')}</p>
            </div>

            {/* 观赏选项 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {t('dubaiNYE.viewingOptions.title')}
              </h2>
              
              {/* Burj Park 付费区域 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-yellow-400">{t('dubaiNYE.viewingOptions.burjPark.title')}</h3>
                <div className="mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="bg-yellow-900/30 rounded p-3 border border-yellow-400/30">
                      <div className="text-yellow-400 font-bold text-sm">{t('dubaiNYE.viewingOptions.burjPark.adultPrice')}</div>
                      <div className="text-slate-300 text-xs">{t('dubaiNYE.viewingOptions.burjPark.adultDetails')}</div>
                    </div>
                    <div className="bg-blue-900/30 rounded p-3 border border-blue-400/30">
                      <div className="text-blue-400 font-bold text-sm">{t('dubaiNYE.viewingOptions.burjPark.childPrice')}</div>
                      <div className="text-slate-300 text-xs">{t('dubaiNYE.viewingOptions.burjPark.childDetails')}</div>
                    </div>
                    <div className="bg-green-900/30 rounded p-3 border border-green-400/30">
                      <div className="text-green-400 font-bold text-sm">{t('dubaiNYE.viewingOptions.burjPark.under5')}</div>
                      <div className="text-slate-300 text-xs">{t('dubaiNYE.viewingOptions.burjPark.under5Details')}</div>
                    </div>
                  </div>
                </div>
                <p className="text-slate-300 mb-4">{t('dubaiNYE.viewingOptions.burjPark.description')}</p>
              </div>

              {/* 免费观赏区域 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-green-400">{t('dubaiNYE.viewingOptions.free.title')}</h3>
                <p className="text-slate-300 mb-4">{t('dubaiNYE.viewingOptions.free.description')}</p>
                <ul className="text-slate-300 space-y-2">
                  <li>• {t('dubaiNYE.viewingOptions.free.location1')}</li>
                  <li>• {t('dubaiNYE.viewingOptions.free.location2')}</li>
                  <li>• {t('dubaiNYE.viewingOptions.free.location3')}</li>
                </ul>
                <div className="mt-4 p-3 bg-green-900/30 rounded border-l-4 border-green-400">
                  <p className="text-green-200 text-sm">{t('dubaiNYE.viewingOptions.free.tip')}</p>
                </div>
              </div>

              {/* 餐饮和游船选项 */}
              <div className="bg-slate-800/50 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-purple-400">{t('dubaiNYE.viewingOptions.dining.title')}</h3>
                <p className="text-slate-300 mb-4">{t('dubaiNYE.viewingOptions.dining.description')}</p>
                <p className="text-slate-300">{t('dubaiNYE.viewingOptions.dining.cruises')}</p>
              </div>
            </section>

            {/* 交通和时间安排 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Navigation className="w-6 h-6 mr-2 text-blue-400" />
                {t('dubaiNYE.transport.title')}
              </h2>
              
              <div className="bg-slate-800/50 rounded-lg p-6 mb-4">
                <h3 className="text-xl font-bold mb-3 text-blue-400">{t('dubaiNYE.transport.metro.title')}</h3>
                <p className="text-slate-300 mb-4">{t('dubaiNYE.transport.metro.description')}</p>
              </div>

              <div className="bg-slate-800/50 rounded-lg p-6 mb-4">
                <h3 className="text-xl font-bold mb-3 text-orange-400">{t('dubaiNYE.transport.roads.title')}</h3>
                <p className="text-slate-300 mb-4">{t('dubaiNYE.transport.roads.description')}</p>
              </div>

              <div className="bg-slate-800/50 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-yellow-400">{t('dubaiNYE.transport.taxi.title')}</h3>
                <p className="text-slate-300">{t('dubaiNYE.transport.taxi.description')}</p>
              </div>
            </section>

            {/* 实用贴士 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('dubaiNYE.tips.title')}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiNYE.tips.tickets.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiNYE.tips.tickets.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiNYE.tips.timing.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiNYE.tips.timing.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiNYE.tips.essentials.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiNYE.tips.essentials.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('dubaiNYE.tips.multiple.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('dubaiNYE.tips.multiple.description')}</p>
                </div>
              </div>
            </section>

            {/* 体验总结 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('dubaiNYE.summary.title')}</h2>
              <div className="bg-gradient-to-r from-yellow-900/50 to-orange-900/50 rounded-lg p-6">
                <p className="text-slate-200 text-lg leading-relaxed">{t('dubaiNYE.summary.description')}</p>
              </div>
            </section>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 快速信息 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('dubaiNYE.quickInfo.title')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-slate-300">{t('dubaiNYE.quickInfo.date')}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-green-400" />
                  <span className="text-slate-300">{t('dubaiNYE.quickInfo.time')}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-red-400" />
                  <span className="text-slate-300">{t('dubaiNYE.quickInfo.location')}</span>
                </div>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 mr-2 text-yellow-400" />
                  <span className="text-slate-300">{t('dubaiNYE.quickInfo.cost')}</span>
                </div>
                <div className="flex items-center">
                  <Ticket className="w-4 h-4 mr-2 text-purple-400" />
                  <span className="text-slate-300">{t('dubaiNYE.quickInfo.booking')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('dubaiNYE.relatedLinks.title')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/sydney-nye-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('dubaiNYE.relatedLinks.sydney')}</span>
                </Link>
                <Link href={`/${locale}/london-nye-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('dubaiNYE.relatedLinks.london')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
