import { useTranslations, useLocale } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Crown } from 'lucide-react'

export default function EventDetail3() {
  const t = useTranslations();
  const locale = useLocale();
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('eventDetail3.backToHome')}</button>
        </Link>
      </div>
      
      {/* 头部信息 */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            {t('events.london.theme')}
          </span>
          <span className="bg-slate-700 text-white px-3 py-1 rounded-full text-sm">
            {t('eventDetail3.category')}
          </span>
        </div>
        <h1 className="text-4xl md:text-5xl font-extrabold mb-4 leading-tight">{t('events.london.title')}</h1>
        <p className="text-xl text-slate-400 mb-6">{t('eventDetail3.subtitle')}</p>
        
        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="flex items-center gap-2 text-slate-300">
            <Calendar className="w-5 h-5 text-blue-400" />
            <span>{t('eventDetail3.date')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <Clock className="w-5 h-5 text-blue-400" />
            <span>{t('eventDetail3.time')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <MapPin className="w-5 h-5 text-blue-400" />
            <span>{t('events.london.location')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <Users className="w-5 h-5 text-blue-400" />
            <span>{t('eventDetail3.attendance')}</span>
          </div>
        </div>
      </div>

      {/* 主图片 */}
      <div className="relative h-96 mb-8 rounded-lg overflow-hidden">
        <Image 
          src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=1200&h=600&fit=crop" 
          alt={t('events.london.title')}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        <div className="absolute bottom-4 left-4 text-white">
          <p className="text-sm opacity-80">{t('eventDetail3.photoCredit')}</p>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Crown className="w-6 h-6 text-blue-400" />
              {t('eventDetail3.aboutTitle')}
            </h2>
            <p className="text-lg text-slate-300 mb-6">{t('eventDetail3.description')}</p>
            
            <h3 className="text-xl font-bold mb-3">{t('eventDetail3.historyTitle')}</h3>
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
              <p className="text-slate-300 mb-3">{t('eventDetail3.historyDesc')}</p>
              <ul className="text-slate-300 space-y-2">
                <li>• {t('eventDetail3.history1')}</li>
                <li>• {t('eventDetail3.history2')}</li>
                <li>• {t('eventDetail3.history3')}</li>
              </ul>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('eventDetail3.viewingTitle')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-blue-400 mb-2">{t('eventDetail3.viewing1Title')}</h4>
                <p className="text-sm text-slate-300 mb-2">{t('eventDetail3.viewing1Desc')}</p>
                <div className="text-xs text-slate-400">
                  <span className="text-green-400">✓</span> {t('eventDetail3.viewing1Pro')}
                </div>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-blue-400 mb-2">{t('eventDetail3.viewing2Title')}</h4>
                <p className="text-sm text-slate-300 mb-2">{t('eventDetail3.viewing2Desc')}</p>
                <div className="text-xs text-slate-400">
                  <span className="text-green-400">✓</span> {t('eventDetail3.viewing2Pro')}
                </div>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-blue-400 mb-2">{t('eventDetail3.viewing3Title')}</h4>
                <p className="text-sm text-slate-300 mb-2">{t('eventDetail3.viewing3Desc')}</p>
                <div className="text-xs text-slate-400">
                  <span className="text-green-400">✓</span> {t('eventDetail3.viewing3Pro')}
                </div>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-blue-400 mb-2">{t('eventDetail3.viewing4Title')}</h4>
                <p className="text-sm text-slate-300 mb-2">{t('eventDetail3.viewing4Desc')}</p>
                <div className="text-xs text-slate-400">
                  <span className="text-green-400">✓</span> {t('eventDetail3.viewing4Pro')}
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('eventDetail3.transportTitle')}</h3>
            <div className="bg-slate-800/50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-bold text-blue-400 mb-2">{t('eventDetail3.transportBefore')}</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• {t('eventDetail3.transport1')}</li>
                    <li>• {t('eventDetail3.transport2')}</li>
                    <li>• {t('eventDetail3.transport3')}</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-bold text-blue-400 mb-2">{t('eventDetail3.transportAfter')}</h4>
                  <ul className="text-sm text-slate-300 space-y-1">
                    <li>• {t('eventDetail3.transport4')}</li>
                    <li>• {t('eventDetail3.transport5')}</li>
                    <li>• {t('eventDetail3.transport6')}</li>
                  </ul>
                </div>
              </div>
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 评分 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail3.ratingTitle')}</h3>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex text-yellow-400">
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5" />
                </div>
                <span className="text-xl font-bold">4.7</span>
              </div>
              <p className="text-sm text-slate-400">{t('eventDetail3.ratingDesc')}</p>
            </div>

            {/* 天气信息 */}
            <div className="bg-gradient-to-br from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail3.weatherTitle')}</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('eventDetail3.temperature')}</span>
                  <span className="font-bold">5-8°C</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('eventDetail3.weather')}</span>
                  <span className="font-bold">{t('eventDetail3.cloudy')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('eventDetail3.clothing')}</span>
                  <span className="font-bold text-blue-400">{t('eventDetail3.warmClothes')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail3.relatedTitle')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/travel-guide-3`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('eventDetail3.guideLink')}</span>
                </Link>
                <Link href={`/${locale}/event-detail-1`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('eventDetail3.nextEvent')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
