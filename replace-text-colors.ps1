# PowerShell script to replace text-slate-300 with text-black in all detail pages

$pages = @(
    "src\app\[locale]\london-nye-guide\page.tsx",
    "src\app\[locale]\sydney-travel-guide\page.tsx", 
    "src\app\[locale]\dubai-travel-guide\page.tsx",
    "src\app\[locale]\europe-travel-guide\page.tsx",
    "src\app\[locale]\archive-detail-1\page.tsx"
)

foreach ($page in $pages) {
    if (Test-Path $page) {
        Write-Host "Processing $page..."
        (Get-Content $page) -replace 'text-slate-300', 'text-black' | Set-Content $page
        Write-Host "Completed $page"
    } else {
        Write-Host "File not found: $page"
    }
}

Write-Host "All pages processed!"
