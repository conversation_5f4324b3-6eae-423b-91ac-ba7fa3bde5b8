'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Sparkles } from 'lucide-react'
import { usePathname } from 'next/navigation'

export default function EventDetail2() {
  const t = useTranslations();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('eventDetail2.backToHome')}</button>
        </Link>
      </div>
      
      {/* 头部信息 */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="bg-gradient-to-r from-yellow-500 to-orange-500 text-black px-3 py-1 rounded-full text-sm font-medium">
            {t('events.dubai.theme')}
          </span>
          <span className="bg-slate-700 text-white px-3 py-1 rounded-full text-sm">
            {t('eventDetail2.category')}
          </span>
        </div>
        <h1 className="text-4xl md:text-5xl font-extrabold mb-4 leading-tight">{t('events.dubai.title')}</h1>
        <p className="text-xl text-slate-400 mb-6">{t('eventDetail2.subtitle')}</p>
        
        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="flex items-center gap-2 text-slate-300">
            <Calendar className="w-5 h-5 text-yellow-400" />
            <span>{t('eventDetail2.date')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <Clock className="w-5 h-5 text-yellow-400" />
            <span>{t('eventDetail2.time')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <MapPin className="w-5 h-5 text-yellow-400" />
            <span>{t('events.dubai.location')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <Users className="w-5 h-5 text-yellow-400" />
            <span>{t('eventDetail2.attendance')}</span>
          </div>
        </div>
      </div>

      {/* 主图片 */}
      <div className="relative h-96 mb-8 rounded-lg overflow-hidden">
        <Image 
          src="https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=1200&h=600&fit=crop" 
          alt={t('events.dubai.title')}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        <div className="absolute bottom-4 left-4 text-white">
          <p className="text-sm opacity-80">{t('eventDetail2.photoCredit')}</p>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Sparkles className="w-6 h-6 text-yellow-400" />
              {t('eventDetail2.aboutTitle')}
            </h2>
            <p className="text-lg text-slate-300 mb-6">{t('eventDetail2.description')}</p>
            
            <h3 className="text-xl font-bold mb-3">{t('eventDetail2.recordsTitle')}</h3>
            <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-lg p-4 mb-6">
              <ul className="text-slate-300 space-y-2">
                <li>• {t('eventDetail2.record1')}</li>
                <li>• {t('eventDetail2.record2')}</li>
                <li>• {t('eventDetail2.record3')}</li>
                <li>• {t('eventDetail2.record4')}</li>
              </ul>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('eventDetail2.venuesTitle')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('eventDetail2.venue1Title')}</h4>
                <p className="text-sm text-slate-300">{t('eventDetail2.venue1Desc')}</p>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('eventDetail2.venue2Title')}</h4>
                <p className="text-sm text-slate-300">{t('eventDetail2.venue2Desc')}</p>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('eventDetail2.venue3Title')}</h4>
                <p className="text-sm text-slate-300">{t('eventDetail2.venue3Desc')}</p>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('eventDetail2.venue4Title')}</h4>
                <p className="text-sm text-slate-300">{t('eventDetail2.venue4Desc')}</p>
              </div>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('eventDetail2.experienceTitle')}</h3>
            <div className="bg-slate-800/50 rounded-lg p-4 mb-6">
              <p className="text-slate-300 mb-4">{t('eventDetail2.experienceDesc')}</p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="p-3 bg-slate-700/50 rounded">
                  <div className="text-2xl font-bold text-yellow-400">360°</div>
                  <div className="text-sm text-slate-400">{t('eventDetail2.experience1')}</div>
                </div>
                <div className="p-3 bg-slate-700/50 rounded">
                  <div className="text-2xl font-bold text-yellow-400">15分钟</div>
                  <div className="text-sm text-slate-400">{t('eventDetail2.experience2')}</div>
                </div>
                <div className="p-3 bg-slate-700/50 rounded">
                  <div className="text-2xl font-bold text-yellow-400">200万</div>
                  <div className="text-sm text-slate-400">{t('eventDetail2.experience3')}</div>
                </div>
              </div>
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 评分 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail2.ratingTitle')}</h3>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex text-yellow-400">
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                </div>
                <span className="text-xl font-bold">4.8</span>
              </div>
              <p className="text-sm text-slate-400">{t('eventDetail2.ratingDesc')}</p>
            </div>

            {/* 门票信息 */}
            <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail2.ticketTitle')}</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('eventDetail2.freeViewing')}</span>
                  <span className="font-bold text-green-400">{t('eventDetail2.free')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('eventDetail2.premiumViewing')}</span>
                  <span className="font-bold text-yellow-400">$200-500</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('eventDetail2.vipExperience')}</span>
                  <span className="font-bold text-orange-400">$1000+</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail2.relatedTitle')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/travel-guide-2`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('eventDetail2.guideLink')}</span>
                </Link>
                <Link href={`/${locale}/event-detail-3`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('eventDetail2.nextEvent')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
