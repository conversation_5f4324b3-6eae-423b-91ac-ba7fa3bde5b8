'use client'

import { useTranslations } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Users, Star, Trophy, Flame, Zap, Globe, Award, Palette } from 'lucide-react'
import { usePathname } from 'next/navigation'

export default function BeijingOlympicsArchive() {
  const t = useTranslations();
  const pathname = usePathname();
  const locale = pathname.split('/')[1] || 'zh';
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('beijingArchive.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('beijingArchive.title')}</h1>
        <p className="text-slate-500 mb-6 text-base md:text-lg">{t('beijingArchive.subtitle')}</p>

        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=400&fit=crop"
            alt={t('beijingArchive.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('beijingArchive.date')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('beijingArchive.location')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主图片 */}
      <div className="relative h-96 mb-8 rounded-lg overflow-hidden">
        <Image 
          src="https://images.unsplash.com/photo-1518611012118-696072aa579a?w=1200&h=600&fit=crop" 
          alt={t('archives.beijing.title')}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        <div className="absolute bottom-4 left-4 text-white">
          <p className="text-sm opacity-80">{t('archiveDetail1.photoCredit')}</p>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-bold mb-4 flex items-center gap-2">
              <Flame className="w-6 h-6 text-yellow-400" />
              {t('archiveDetail1.aboutTitle')}
            </h2>
            <p className="text-lg text-slate-300 mb-6">{t('archiveDetail1.description')}</p>
            
            <h3 className="text-xl font-bold mb-3">{t('archiveDetail1.highlightsTitle')}</h3>
            <div className="bg-gradient-to-r from-yellow-500/10 to-red-500/10 border border-yellow-500/20 rounded-lg p-4 mb-6">
              <ul className="text-slate-300 space-y-3">
                <li className="flex items-start gap-2">
                  <Trophy className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <span>{t('archiveDetail1.highlight1')}</span>
                </li>
                <li className="flex items-start gap-2">
                  <Trophy className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <span>{t('archiveDetail1.highlight2')}</span>
                </li>
                <li className="flex items-start gap-2">
                  <Trophy className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <span>{t('archiveDetail1.highlight3')}</span>
                </li>
                <li className="flex items-start gap-2">
                  <Trophy className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <span>{t('archiveDetail1.highlight4')}</span>
                </li>
              </ul>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('archiveDetail1.sequenceTitle')}</h3>
            <div className="space-y-4 mb-6">
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('archiveDetail1.sequence1Title')}</h4>
                <p className="text-slate-300 text-sm">{t('archiveDetail1.sequence1Desc')}</p>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('archiveDetail1.sequence2Title')}</h4>
                <p className="text-slate-300 text-sm">{t('archiveDetail1.sequence2Desc')}</p>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('archiveDetail1.sequence3Title')}</h4>
                <p className="text-slate-300 text-sm">{t('archiveDetail1.sequence3Desc')}</p>
              </div>
              <div className="bg-slate-800/50 rounded-lg p-4">
                <h4 className="font-bold text-yellow-400 mb-2">{t('archiveDetail1.sequence4Title')}</h4>
                <p className="text-slate-300 text-sm">{t('archiveDetail1.sequence4Desc')}</p>
              </div>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('archiveDetail1.legacyTitle')}</h3>
            <div className="bg-gradient-to-r from-red-500/10 to-yellow-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
              <p className="text-slate-300 mb-4">{t('archiveDetail1.legacyDesc')}</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="text-center p-3 bg-slate-700/50 rounded">
                  <div className="text-2xl font-bold text-yellow-400">29,000</div>
                  <div className="text-sm text-slate-400">{t('archiveDetail1.legacy1')}</div>
                </div>
                <div className="text-center p-3 bg-slate-700/50 rounded">
                  <div className="text-2xl font-bold text-yellow-400">40亿</div>
                  <div className="text-sm text-slate-400">{t('archiveDetail1.legacy2')}</div>
                </div>
              </div>
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 历史评分 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('archiveDetail1.ratingTitle')}</h3>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex text-yellow-400">
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                </div>
                <span className="text-xl font-bold">5.0</span>
              </div>
              <p className="text-sm text-slate-400">{t('archiveDetail1.ratingDesc')}</p>
            </div>

            {/* 技术规格 */}
            <div className="bg-gradient-to-br from-yellow-500/10 to-red-500/10 border border-yellow-500/20 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('archiveDetail1.specsTitle')}</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('archiveDetail1.duration')}</span>
                  <span className="font-bold">56分钟</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('archiveDetail1.fireworks')}</span>
                  <span className="font-bold">29,000发</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('archiveDetail1.cost')}</span>
                  <span className="font-bold">$100M+</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">{t('archiveDetail1.designers')}</span>
                  <span className="font-bold">张艺谋团队</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('archiveDetail1.relatedTitle')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/event-detail-1`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('archiveDetail1.modernEvents')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
