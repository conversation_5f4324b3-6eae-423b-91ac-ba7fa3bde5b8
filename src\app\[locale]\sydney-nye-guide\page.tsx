import { useTranslations, useLocale } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star, Navigation, AlertCircle, Ticket } from 'lucide-react'

export default function SydneyNYEGuide() {
  const t = useTranslations();
  const locale = useLocale();
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('sydneyNYE.backToHome')}</button>
        </Link>
      </div>

      {/* 标题和简介 */}
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-extrabold mb-4 leading-tight">{t('sydneyNYE.title')}</h1>
        <p className="text-slate-500 mb-6 text-base md:text-lg">{t('sydneyNYE.subtitle')}</p>
        
        {/* 头图 */}
        <div className="relative w-full h-64 md:h-80 mb-6 rounded-lg overflow-hidden">
          <Image
            src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop"
            alt={t('sydneyNYE.title')}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          <div className="absolute bottom-4 left-4 text-white">
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1" />
                {t('sydneyNYE.date')}
              </div>
              <div className="flex items-center">
                <MapPin className="w-4 h-4 mr-1" />
                {t('sydneyNYE.location')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            {/* 简介 */}
            <div className="mb-8">
              <p className="text-lg text-slate-300 mb-4">{t('sydneyNYE.intro')}</p>
            </div>

            {/* 观赏选项 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Star className="w-6 h-6 mr-2 text-yellow-400" />
                {t('sydneyNYE.viewingOptions.title')}
              </h2>
              
              {/* 免费观赏点 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-green-400">{t('sydneyNYE.viewingOptions.free.title')}</h3>
                <p className="text-slate-300 mb-4">{t('sydneyNYE.viewingOptions.free.description')}</p>
                <ul className="text-slate-300 space-y-2">
                  <li>• {t('sydneyNYE.viewingOptions.free.location1')}</li>
                  <li>• {t('sydneyNYE.viewingOptions.free.location2')}</li>
                  <li>• {t('sydneyNYE.viewingOptions.free.location3')}</li>
                  <li>• {t('sydneyNYE.viewingOptions.free.location4')}</li>
                  <li>• {t('sydneyNYE.viewingOptions.free.location5')}</li>
                </ul>
                <div className="mt-4 p-3 bg-blue-900/30 rounded border-l-4 border-blue-400">
                  <p className="text-blue-200 text-sm">{t('sydneyNYE.viewingOptions.free.note')}</p>
                </div>
              </div>

              {/* 付费观赏点 */}
              <div className="bg-slate-800/50 rounded-lg p-6 mb-6">
                <h3 className="text-xl font-bold mb-3 text-blue-400">{t('sydneyNYE.viewingOptions.ticketed.title')}</h3>
                <p className="text-slate-300 mb-4">{t('sydneyNYE.viewingOptions.ticketed.description')}</p>
              </div>

              {/* 高端体验 */}
              <div className="bg-slate-800/50 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-purple-400">{t('sydneyNYE.viewingOptions.premium.title')}</h3>
                <p className="text-slate-300 mb-4">{t('sydneyNYE.viewingOptions.premium.description')}</p>
                <div className="mt-4 p-3 bg-purple-900/30 rounded border-l-4 border-purple-400">
                  <p className="text-purple-200 text-sm">{t('sydneyNYE.viewingOptions.premium.pricing')}</p>
                </div>
              </div>
            </section>

            {/* 交通和物流 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <Navigation className="w-6 h-6 mr-2 text-blue-400" />
                {t('sydneyNYE.transport.title')}
              </h2>
              
              <div className="bg-slate-800/50 rounded-lg p-6 mb-4">
                <h3 className="text-xl font-bold mb-3 text-red-400">{t('sydneyNYE.transport.crowds.title')}</h3>
                <p className="text-slate-300 mb-4">{t('sydneyNYE.transport.crowds.description')}</p>
              </div>

              <div className="bg-slate-800/50 rounded-lg p-6 mb-4">
                <h3 className="text-xl font-bold mb-3 text-green-400">{t('sydneyNYE.transport.public.title')}</h3>
                <p className="text-slate-300 mb-4">{t('sydneyNYE.transport.public.description')}</p>
              </div>

              <div className="bg-slate-800/50 rounded-lg p-6">
                <h3 className="text-xl font-bold mb-3 text-orange-400">{t('sydneyNYE.transport.roads.title')}</h3>
                <p className="text-slate-300">{t('sydneyNYE.transport.roads.description')}</p>
              </div>
            </section>

            {/* 实用贴士 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4 flex items-center">
                <AlertCircle className="w-6 h-6 mr-2 text-yellow-400" />
                {t('sydneyNYE.tips.title')}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('sydneyNYE.tips.timing.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('sydneyNYE.tips.timing.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('sydneyNYE.tips.essentials.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('sydneyNYE.tips.essentials.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('sydneyNYE.tips.weather.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('sydneyNYE.tips.weather.description')}</p>
                </div>
                <div className="bg-slate-800/50 rounded-lg p-4">
                  <h4 className="font-bold text-yellow-400 mb-2">{t('sydneyNYE.tips.family.title')}</h4>
                  <p className="text-slate-300 text-sm">{t('sydneyNYE.tips.family.description')}</p>
                </div>
              </div>
            </section>

            {/* 体验总结 */}
            <section className="mb-8">
              <h2 className="text-2xl font-bold mb-4">{t('sydneyNYE.summary.title')}</h2>
              <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-lg p-6">
                <p className="text-slate-200 text-lg leading-relaxed">{t('sydneyNYE.summary.description')}</p>
              </div>
            </section>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 快速信息 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('sydneyNYE.quickInfo.title')}</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-blue-400" />
                  <span className="text-slate-300">{t('sydneyNYE.quickInfo.date')}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-4 h-4 mr-2 text-green-400" />
                  <span className="text-slate-300">{t('sydneyNYE.quickInfo.time')}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-4 h-4 mr-2 text-red-400" />
                  <span className="text-slate-300">{t('sydneyNYE.quickInfo.location')}</span>
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-2 text-purple-400" />
                  <span className="text-slate-300">{t('sydneyNYE.quickInfo.crowd')}</span>
                </div>
                <div className="flex items-center">
                  <Ticket className="w-4 h-4 mr-2 text-yellow-400" />
                  <span className="text-slate-300">{t('sydneyNYE.quickInfo.cost')}</span>
                </div>
              </div>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('sydneyNYE.relatedLinks.title')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/dubai-nye-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('sydneyNYE.relatedLinks.dubai')}</span>
                </Link>
                <Link href={`/${locale}/london-nye-guide`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('sydneyNYE.relatedLinks.london')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
