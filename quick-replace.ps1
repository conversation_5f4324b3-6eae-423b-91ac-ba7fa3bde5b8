# Quick replace script for remaining text-slate-300
$content = Get-Content "src\app\[locale]\london-nye-guide\page.tsx" -Raw
$content = $content -replace 'text-slate-300', 'text-black'
Set-Content "src\app\[locale]\london-nye-guide\page.tsx" -Value $content

Write-Host "London NYE guide updated!"

# Do the same for other files
$files = @(
    "src\app\[locale]\sydney-travel-guide\page.tsx",
    "src\app\[locale]\dubai-travel-guide\page.tsx", 
    "src\app\[locale]\europe-travel-guide\page.tsx",
    "src\app\[locale]\archive-detail-1\page.tsx"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        $content = $content -replace 'text-slate-300', 'text-black'
        Set-Content $file -Value $content
        Write-Host "Updated $file"
    }
}

Write-Host "All files updated!"
