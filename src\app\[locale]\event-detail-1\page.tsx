import { useTranslations, useLocale } from 'next-intl'
import Image from 'next/image'
import Link from 'next/link'
import { Calendar, MapPin, Clock, Users, Star } from 'lucide-react'

export default function EventDetail1() {
  const t = useTranslations();
  const locale = useLocale();
  return (
    <div className="max-w-4xl mx-auto py-10 px-4">
      {/* 返回主页按钮 */}
      <div className="mb-6">
        <Link href={`/${locale}`}>
          <button className="px-4 py-2 rounded bg-slate-800 text-white hover:bg-slate-700 transition">{t('eventDetail1.backToHome')}</button>
        </Link>
      </div>
      
      {/* 头部信息 */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-4">
          <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-medium">
            {t('events.sydney.theme')}
          </span>
          <span className="bg-slate-700 text-white px-3 py-1 rounded-full text-sm">
            {t('eventDetail1.category')}
          </span>
        </div>
        <h1 className="text-4xl md:text-5xl font-extrabold mb-4 leading-tight">{t('events.sydney.title')}</h1>
        <p className="text-xl text-slate-400 mb-6">{t('eventDetail1.subtitle')}</p>
        
        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <div className="flex items-center gap-2 text-slate-300">
            <Calendar className="w-5 h-5 text-orange-400" />
            <span>{t('eventDetail1.date')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <Clock className="w-5 h-5 text-orange-400" />
            <span>{t('eventDetail1.time')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <MapPin className="w-5 h-5 text-orange-400" />
            <span>{t('events.sydney.location')}</span>
          </div>
          <div className="flex items-center gap-2 text-slate-300">
            <Users className="w-5 h-5 text-orange-400" />
            <span>{t('eventDetail1.attendance')}</span>
          </div>
        </div>
      </div>

      {/* 主图片 */}
      <div className="relative h-96 mb-8 rounded-lg overflow-hidden">
        <Image 
          src="https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=1200&h=600&fit=crop" 
          alt={t('events.sydney.title')}
          fill
          className="object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        <div className="absolute bottom-4 left-4 text-white">
          <p className="text-sm opacity-80">{t('eventDetail1.photoCredit')}</p>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2">
          <article className="prose prose-invert max-w-none">
            <h2 className="text-2xl font-bold mb-4">{t('eventDetail1.aboutTitle')}</h2>
            <p className="text-lg text-slate-300 mb-6">{t('eventDetail1.description')}</p>
            
            <h3 className="text-xl font-bold mb-3">{t('eventDetail1.highlightsTitle')}</h3>
            <ul className="mb-6 text-slate-300">
              <li>{t('eventDetail1.highlight1')}</li>
              <li>{t('eventDetail1.highlight2')}</li>
              <li>{t('eventDetail1.highlight3')}</li>
              <li>{t('eventDetail1.highlight4')}</li>
            </ul>

            <h3 className="text-xl font-bold mb-3">{t('eventDetail1.scheduleTitle')}</h3>
            <div className="bg-slate-800/50 rounded-lg p-4 mb-6">
              <div className="space-y-3">
                <div className="flex justify-between items-center border-b border-slate-600 pb-2">
                  <span className="font-medium">21:00</span>
                  <span className="text-slate-300">{t('eventDetail1.schedule1')}</span>
                </div>
                <div className="flex justify-between items-center border-b border-slate-600 pb-2">
                  <span className="font-medium">21:30</span>
                  <span className="text-slate-300">{t('eventDetail1.schedule2')}</span>
                </div>
                <div className="flex justify-between items-center border-b border-slate-600 pb-2">
                  <span className="font-medium">00:00</span>
                  <span className="text-slate-300">{t('eventDetail1.schedule3')}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium">00:20</span>
                  <span className="text-slate-300">{t('eventDetail1.schedule4')}</span>
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold mb-3">{t('eventDetail1.tipsTitle')}</h3>
            <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4 mb-6">
              <ul className="text-slate-300 space-y-2">
                <li>• {t('eventDetail1.tip1')}</li>
                <li>• {t('eventDetail1.tip2')}</li>
                <li>• {t('eventDetail1.tip3')}</li>
                <li>• {t('eventDetail1.tip4')}</li>
              </ul>
            </div>
          </article>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* 评分 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail1.ratingTitle')}</h3>
              <div className="flex items-center gap-2 mb-2">
                <div className="flex text-yellow-400">
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                  <Star className="w-5 h-5 fill-current" />
                </div>
                <span className="text-xl font-bold">4.9</span>
              </div>
              <p className="text-sm text-slate-400">{t('eventDetail1.ratingDesc')}</p>
            </div>

            {/* 相关链接 */}
            <div className="bg-slate-800/50 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">{t('eventDetail1.relatedTitle')}</h3>
              <div className="space-y-3">
                <Link href={`/${locale}/travel-guide-1`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('eventDetail1.guideLink')}</span>
                </Link>
                <Link href={`/${locale}/event-detail-2`} className="block p-3 bg-slate-700/50 rounded-lg hover:bg-slate-700 transition">
                  <span className="text-blue-400 font-medium">{t('eventDetail1.nextEvent')}</span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
